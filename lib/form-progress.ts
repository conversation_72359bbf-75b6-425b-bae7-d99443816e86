'use client';

/**
 * Form Progress Preservation Utility
 * Handles saving and restoring form data to prevent data loss
 */

export interface FormProgressOptions {
  storageKey: string;
  expirationHours?: number;
  excludeFields?: string[];
}

export interface SavedFormData {
  data: Record<string, any>;
  timestamp: number;
  expiresAt: number;
}

class FormProgressManager {
  private static instance: FormProgressManager;
  private readonly PREFIX = 'form_progress_';

  static getInstance(): FormProgressManager {
    if (!FormProgressManager.instance) {
      FormProgressManager.instance = new FormProgressManager();
    }
    return FormProgressManager.instance;
  }

  /**
   * Save form data to localStorage with expiration
   */
  saveProgress(
    storageKey: string, 
    data: Record<string, any>, 
    options: Partial<FormProgressOptions> = {}
  ): void {
    if (typeof window === 'undefined') return;

    const { expirationHours = 24, excludeFields = [] } = options;
    
    try {
      // Filter out excluded fields
      const filteredData = Object.entries(data).reduce((acc, [key, value]) => {
        if (!excludeFields.includes(key)) {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      const now = Date.now();
      const expiresAt = now + (expirationHours * 60 * 60 * 1000);

      const savedData: SavedFormData = {
        data: filteredData,
        timestamp: now,
        expiresAt
      };

      localStorage.setItem(
        `${this.PREFIX}${storageKey}`, 
        JSON.stringify(savedData)
      );
    } catch (error) {
      console.warn('Failed to save form progress:', error);
    }
  }

  /**
   * Load form data from localStorage
   */
  loadProgress(storageKey: string): Record<string, any> | null {
    if (typeof window === 'undefined') return null;

    try {
      const saved = localStorage.getItem(`${this.PREFIX}${storageKey}`);
      if (!saved) return null;

      const parsedData: SavedFormData = JSON.parse(saved);
      
      // Check if data has expired
      if (Date.now() > parsedData.expiresAt) {
        this.clearProgress(storageKey);
        return null;
      }

      return parsedData.data;
    } catch (error) {
      console.warn('Failed to load form progress:', error);
      this.clearProgress(storageKey);
      return null;
    }
  }

  /**
   * Clear saved form data
   */
  clearProgress(storageKey: string): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem(`${this.PREFIX}${storageKey}`);
    } catch (error) {
      console.warn('Failed to clear form progress:', error);
    }
  }

  /**
   * Check if there's saved progress for a form
   */
  hasProgress(storageKey: string): boolean {
    return this.loadProgress(storageKey) !== null;
  }

  /**
   * Get all saved form keys (for debugging/cleanup)
   */
  getAllProgressKeys(): string[] {
    if (typeof window === 'undefined') return [];

    try {
      const keys: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.PREFIX)) {
          keys.push(key.replace(this.PREFIX, ''));
        }
      }
      return keys;
    } catch (error) {
      console.warn('Failed to get progress keys:', error);
      return [];
    }
  }

  /**
   * Clear all expired form progress data
   */
  cleanupExpiredProgress(): void {
    if (typeof window === 'undefined') return;

    try {
      const keys = this.getAllProgressKeys();
      const now = Date.now();

      keys.forEach(key => {
        try {
          const saved = localStorage.getItem(`${this.PREFIX}${key}`);
          if (saved) {
            const parsedData: SavedFormData = JSON.parse(saved);
            if (now > parsedData.expiresAt) {
              localStorage.removeItem(`${this.PREFIX}${key}`);
            }
          }
        } catch (error) {
          // If we can't parse the data, remove it
          localStorage.removeItem(`${this.PREFIX}${key}`);
        }
      });
    } catch (error) {
      console.warn('Failed to cleanup expired progress:', error);
    }
  }

  /**
   * Get progress metadata (timestamp, expiration)
   */
  getProgressMetadata(storageKey: string): { timestamp: number; expiresAt: number } | null {
    if (typeof window === 'undefined') return null;

    try {
      const saved = localStorage.getItem(`${this.PREFIX}${storageKey}`);
      if (!saved) return null;

      const parsedData: SavedFormData = JSON.parse(saved);
      return {
        timestamp: parsedData.timestamp,
        expiresAt: parsedData.expiresAt
      };
    } catch (error) {
      return null;
    }
  }
}

// Export singleton instance
export const formProgressManager = FormProgressManager.getInstance();

// Convenience hooks for React components
export function useFormProgress(storageKey: string, options: Partial<FormProgressOptions> = {}) {
  const saveProgress = (data: Record<string, any>) => {
    formProgressManager.saveProgress(storageKey, data, options);
  };

  const loadProgress = () => {
    return formProgressManager.loadProgress(storageKey);
  };

  const clearProgress = () => {
    formProgressManager.clearProgress(storageKey);
  };

  const hasProgress = () => {
    return formProgressManager.hasProgress(storageKey);
  };

  return {
    saveProgress,
    loadProgress,
    clearProgress,
    hasProgress
  };
}

// Auto-cleanup on page load
if (typeof window !== 'undefined') {
  // Clean up expired progress on page load
  formProgressManager.cleanupExpiredProgress();
  
  // Set up periodic cleanup (every hour)
  setInterval(() => {
    formProgressManager.cleanupExpiredProgress();
  }, 60 * 60 * 1000);
}

// Utility function to merge saved progress with initial values
export function mergeWithSavedProgress<T extends Record<string, any>>(
  initialValues: T,
  savedProgress: Record<string, any> | null,
  options: {
    preserveArrays?: boolean;
    preserveObjects?: boolean;
    excludeFields?: string[];
  } = {}
): T {
  if (!savedProgress) return initialValues;

  const { preserveArrays = true, preserveObjects = true, excludeFields = [] } = options;

  const merged = { ...initialValues };

  Object.entries(savedProgress).forEach(([key, value]) => {
    // Skip excluded fields
    if (excludeFields.includes(key)) return;

    // Skip if the field doesn't exist in initial values
    if (!(key in initialValues)) return;

    // Handle different value types
    if (value === null || value === undefined) {
      merged[key as keyof T] = value;
    } else if (Array.isArray(value) && preserveArrays) {
      merged[key as keyof T] = value;
    } else if (typeof value === 'object' && preserveObjects) {
      merged[key as keyof T] = value;
    } else {
      merged[key as keyof T] = value;
    }
  });

  return merged;
}

// Warning component for unsaved changes
export function getUnsavedChangesWarning(): string {
  return 'You have unsaved changes. Are you sure you want to leave this page?';
}

// Browser beforeunload handler
export function setupUnsavedChangesWarning(hasUnsavedChanges: boolean): () => void {
  const handleBeforeUnload = (e: BeforeUnloadEvent) => {
    if (hasUnsavedChanges) {
      e.preventDefault();
      e.returnValue = getUnsavedChangesWarning();
      return getUnsavedChangesWarning();
    }
  };

  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }

  return () => {};
}

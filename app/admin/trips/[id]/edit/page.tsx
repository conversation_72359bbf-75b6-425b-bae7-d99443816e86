'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import TripFormEnhanced from '../../components/TripFormEnhanced';
import { Trip, TripFormData } from '@/types/trip';
import AdminLayout from '@/components/layout/AdminLayout';
import { useToast } from '@/hooks/useToast';
import { useTrip, useUpdateTrip } from '@/hooks/useTrips';

export default function EditTripPage() {
  const params = useParams();
  const router = useRouter();
  const toast = useToast();
  const [error, setError] = useState<string | null>(null);

  // Use React Query hooks
  const { data: trip, isLoading: tripLoading, error: tripError } = useTrip(params.id as string);
  const updateTripMutation = useUpdateTrip();

  const handleSubmit = async (data: TripFormData) => {
    const toastId = toast.loading('Updating trip...');

    try {
      if (!trip) {
        throw new Error('Trip data is missing');
      }

      await updateTripMutation.mutateAsync({
        ...data,
        id: params.id as string,
        is_active: trip.is_active,
        is_featured: trip.is_featured
      });

      toast.success('Trip updated successfully!');
      router.push('/admin/trips' as any);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update trip';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      toast.dismiss(toastId);
    }
  };

  if (error || tripError) {
    return (
      <AdminLayout>
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error || (tripError instanceof Error ? tripError.message : 'An error occurred')}</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (tripLoading || !trip) {
    return (
      <AdminLayout>
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
          <p className="mt-2 text-gray-600">Loading trip...</p>
        </div>
      </AdminLayout>
    );
  }

  const formData: TripFormData = {
    title: trip.title,
    description: trip.description || '',
    detailed_description: trip.detailed_description || '',
    destination: trip.destination,
    days: trip.days,
    nights: trip.nights,
    price_per_person: trip.price_per_person,
    difficulty: trip.difficulty,
    inclusions: trip.inclusions || [],
    exclusions: trip.exclusions || [],
    itinerary: trip.itinerary || [],
    featured_image_url: trip.featured_image_url || '',
    is_active: trip.is_active,
    is_featured: trip.is_featured,
    is_trek: trip.is_trek || false,
    min_age: trip.min_age || null,
    max_age: trip.max_age || null,

    category: trip.category || '',
    mode_of_travel: trip.mode_of_travel || '',
    pickup_location: trip.pickup_location || '',
    drop_location: trip.drop_location || '',
    property_used: trip.property_used || '',
    activities: trip.activities || [],
    optional_activities: trip.optional_activities || [],
    benefits: trip.benefits || [],
    safety_supervision: trip.safety_supervision || [],
    things_to_carry: trip.things_to_carry || [],
    available_dates: trip.available_dates || [],
    payment_terms: trip.payment_terms || '',
    cancellation_policy: trip.cancellation_policy,
    special_notes: trip.special_notes || [],
    auto_deactivation_date: trip.auto_deactivation_date || null,
  };

  return (
    <AdminLayout>
      <div className="mb-8">
        <Link href="/admin/trips" className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md flex items-center w-fit mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Trips
        </Link>
      </div>
      <TripFormEnhanced
        initialData={formData}
        onSubmit={handleSubmit}
        isLoading={updateTripMutation.isPending}
        onCancel={() => router.back()}
        mode="edit"
      />
    </AdminLayout>
  );
} 
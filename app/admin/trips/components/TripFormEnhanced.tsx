'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Save, AlertTriangle, CheckCircle2, Clock } from 'lucide-react';
import { TripFormData } from '@/types/trip';
import { tripSchema } from '@/lib/validation-schemas';
import { useFormValidation } from '@/hooks/useFormValidation';
import { FormField, FormInput, FormTextarea, FormSelect, FormSection } from '@/components/ui/FormField';
import { setupUnsavedChangesWarning } from '@/lib/form-progress';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { useToast } from '@/hooks/useToast';

interface TripFormEnhancedProps {
  initialData?: Partial<TripFormData>;
  onSubmit: (data: TripFormData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
}

const DIFFICULTY_OPTIONS = [
  { value: 'easy', label: 'Easy' },
  { value: 'moderate', label: 'Moderate' },
  { value: 'challenging', label: 'Challenging' },
  { value: 'difficult', label: 'Difficult' }
];

const DEFAULT_FORM_DATA: Partial<TripFormData> = {
  title: '',
  description: '',
  detailed_description: '',
  destination: '',
  days: 1,
  nights: 0,
  price_per_person: 0,
  difficulty: 'easy',
  min_age: null,
  max_age: null,
  category: '',
  mode_of_travel: '',
  pickup_location: '',
  drop_location: '',
  property_used: '',
  is_trek: false,
  is_active: true,
  is_featured: false,
  featured_image_url: '',
  inclusions: [],
  exclusions: [],
  activities: [],
  optional_activities: [],
  benefits: [],
  safety_supervision: [],
  things_to_carry: [],
  available_dates: [],
  special_notes: [],
  itinerary: []
};

export default function TripFormEnhanced({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  mode = 'create'
}: TripFormEnhancedProps) {
  const router = useRouter();
  const toast = useToast();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [showProgressWarning, setShowProgressWarning] = useState(false);

  // Merge initial data with defaults
  const formInitialData = { ...DEFAULT_FORM_DATA, ...initialData };

  // Form validation hook
  const {
    values,
    errors,
    isValid,
    isFieldValid,
    getFieldError,
    validateForm,
    updateField,
    updateFields,
    setValues,
    hasUnsavedChanges,
    markAsSaved
  } = useFormValidation(formInitialData, {
    schema: tripSchema,
    validateOnChange: true,
    preserveProgress: true,
    storageKey: mode === 'create' ? 'trip_create' : `trip_edit_${initialData?.id || 'unknown'}`
  });

  // Auto-calculate nights when days change
  useEffect(() => {
    if (values.days && typeof values.days === 'number') {
      const calculatedNights = Math.max(0, values.days - 1);
      if (values.nights !== calculatedNights) {
        updateField('nights', calculatedNights);
      }
    }
  }, [values.days, values.nights, updateField]);

  // Set up unsaved changes warning
  useEffect(() => {
    const cleanup = setupUnsavedChangesWarning(hasUnsavedChanges);
    return cleanup;
  }, [hasUnsavedChanges]);

  // Show progress warning if there are unsaved changes
  useEffect(() => {
    setShowProgressWarning(hasUnsavedChanges);
  }, [hasUnsavedChanges]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError(null);

    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the validation errors before submitting');
      return;
    }

    try {
      await onSubmit(values as TripFormData);
      markAsSaved();
      toast.success(`Trip ${mode === 'create' ? 'created' : 'updated'} successfully!`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : `Failed to ${mode} trip`;
      setSubmitError(errorMessage);
      toast.error(errorMessage);
    }
  };

  // Handle cancel with unsaved changes check
  const handleCancel = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm(
        'You have unsaved changes. Are you sure you want to cancel?'
      );
      if (!confirmed) return;
    }

    if (onCancel) {
      onCancel();
    } else {
      router.back();
    }
  };

  // Handle field changes with validation
  const handleFieldChange = (field: keyof TripFormData, value: any) => {
    updateField(field, value);
  };

  // Handle number field changes
  const handleNumberChange = (field: keyof TripFormData, value: string) => {
    const numValue = value === '' ? 0 : parseFloat(value);
    if (!isNaN(numValue)) {
      updateField(field, numValue);
    }
  };

  // Handle nullable number field changes
  const handleNullableNumberChange = (field: keyof TripFormData, value: string) => {
    if (value === '') {
      updateField(field, null);
    } else {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        updateField(field, numValue);
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Warning */}
      {showProgressWarning && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 bg-amber-50 border border-amber-200 rounded-lg p-4"
        >
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-amber-600" />
            <span className="text-sm text-amber-800">
              You have unsaved changes. Your progress is automatically saved locally.
            </span>
          </div>
        </motion.div>
      )}

      {/* Submit Error */}
      {submitError && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4"
        >
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <span className="text-sm text-red-800">{submitError}</span>
          </div>
        </motion.div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <FormSection
          title="Basic Information"
          description="Essential details about the trip"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <FormInput
                name="title"
                label="Trip Title"
                required
                value={values.title || ''}
                onChange={(e) => handleFieldChange('title', e.target.value)}
                error={getFieldError('title')}
                isValid={isFieldValid('title')}
                placeholder="Enter trip title"
                description="A compelling title that describes your trip"
              />
            </div>

            <FormInput
              name="destination"
              label="Destination"
              required
              value={values.destination || ''}
              onChange={(e) => handleFieldChange('destination', e.target.value)}
              error={getFieldError('destination')}
              isValid={isFieldValid('destination')}
              placeholder="Enter destination"
            />

            <FormSelect
              name="difficulty"
              label="Difficulty Level"
              required
              value={values.difficulty || ''}
              onChange={(e) => handleFieldChange('difficulty', e.target.value)}
              error={getFieldError('difficulty')}
              isValid={isFieldValid('difficulty')}
              options={DIFFICULTY_OPTIONS}
              placeholder="Select difficulty"
            />
          </div>

          <FormTextarea
            name="description"
            label="Short Description"
            required
            value={values.description || ''}
            onChange={(e) => handleFieldChange('description', e.target.value)}
            error={getFieldError('description')}
            isValid={isFieldValid('description')}
            placeholder="Brief description of the trip"
            rows={3}
            description="A short summary that will appear in trip listings"
          />
        </FormSection>

        {/* Duration and Pricing */}
        <FormSection
          title="Duration & Pricing"
          description="Trip duration and cost information"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <FormInput
              name="days"
              label="Days"
              type="number"
              required
              value={values.days?.toString() || ''}
              onChange={(e) => handleNumberChange('days', e.target.value)}
              error={getFieldError('days')}
              isValid={isFieldValid('days')}
              min="1"
              max="30"
            />

            <FormInput
              name="nights"
              label="Nights"
              type="number"
              required
              value={values.nights?.toString() || ''}
              disabled
              error={getFieldError('nights')}
              isValid={isFieldValid('nights')}
              description="Automatically calculated as days - 1"
            />

            <FormInput
              name="price_per_person"
              label="Price per Person (₹)"
              type="number"
              required
              value={values.price_per_person?.toString() || ''}
              onChange={(e) => handleNumberChange('price_per_person', e.target.value)}
              error={getFieldError('price_per_person')}
              isValid={isFieldValid('price_per_person')}
              min="0"
              step="100"
            />
          </div>
        </FormSection>

        {/* Age Requirements */}
        <FormSection
          title="Age Requirements"
          description="Optional age restrictions for participants"
          collapsible
          defaultExpanded={false}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormInput
              name="min_age"
              label="Minimum Age"
              type="number"
              value={values.min_age?.toString() || ''}
              onChange={(e) => handleNullableNumberChange('min_age', e.target.value)}
              error={getFieldError('min_age')}
              isValid={isFieldValid('min_age')}
              min="1"
              max="100"
              placeholder="No minimum age"
            />

            <FormInput
              name="max_age"
              label="Maximum Age"
              type="number"
              value={values.max_age?.toString() || ''}
              onChange={(e) => handleNullableNumberChange('max_age', e.target.value)}
              error={getFieldError('max_age')}
              isValid={isFieldValid('max_age')}
              min="1"
              max="100"
              placeholder="No maximum age"
            />
          </div>
        </FormSection>

        {/* Detailed Description */}
        <FormSection
          title="Detailed Description"
          description="Comprehensive information about the trip"
        >
          <FormTextarea
            name="detailed_description"
            label="Detailed Description"
            value={values.detailed_description || ''}
            onChange={(e) => handleFieldChange('detailed_description', e.target.value)}
            error={getFieldError('detailed_description')}
            isValid={isFieldValid('detailed_description')}
            placeholder="Provide detailed information about the trip, what's included, what to expect, etc."
            rows={6}
            description="Detailed information that will appear on the trip detail page"
          />
        </FormSection>

        {/* Travel Information */}
        <FormSection
          title="Travel Information"
          description="Transportation and location details"
          collapsible
          defaultExpanded={false}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormInput
              name="mode_of_travel"
              label="Mode of Travel"
              value={values.mode_of_travel || ''}
              onChange={(e) => handleFieldChange('mode_of_travel', e.target.value)}
              error={getFieldError('mode_of_travel')}
              isValid={isFieldValid('mode_of_travel')}
              placeholder="e.g., Bus, Train, Flight"
            />

            <FormInput
              name="category"
              label="Category"
              value={values.category || ''}
              onChange={(e) => handleFieldChange('category', e.target.value)}
              error={getFieldError('category')}
              isValid={isFieldValid('category')}
              placeholder="e.g., Adventure, Cultural, Leisure"
            />

            <FormInput
              name="pickup_location"
              label="Pickup Location"
              value={values.pickup_location || ''}
              onChange={(e) => handleFieldChange('pickup_location', e.target.value)}
              error={getFieldError('pickup_location')}
              isValid={isFieldValid('pickup_location')}
              placeholder="Where participants will be picked up"
            />

            <FormInput
              name="drop_location"
              label="Drop Location"
              value={values.drop_location || ''}
              onChange={(e) => handleFieldChange('drop_location', e.target.value)}
              error={getFieldError('drop_location')}
              isValid={isFieldValid('drop_location')}
              placeholder="Where participants will be dropped off"
            />

            <div className="md:col-span-2">
              <FormInput
                name="property_used"
                label="Property/Accommodation"
                value={values.property_used || ''}
                onChange={(e) => handleFieldChange('property_used', e.target.value)}
                error={getFieldError('property_used')}
                isValid={isFieldValid('property_used')}
                placeholder="Hotels, camps, or other accommodations used"
              />
            </div>
          </div>
        </FormSection>

        {/* Trip Settings */}
        <FormSection
          title="Trip Settings"
          description="Additional trip configuration"
          collapsible
          defaultExpanded={false}
        >
          <div className="space-y-4">
            <div className="flex items-center space-x-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={values.is_trek || false}
                  onChange={(e) => handleFieldChange('is_trek', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">This is a trek</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={values.is_featured || false}
                  onChange={(e) => handleFieldChange('is_featured', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Featured trip</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={values.is_active || false}
                  onChange={(e) => handleFieldChange('is_active', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Active</span>
              </label>
            </div>

            <FormInput
              name="featured_image_url"
              label="Featured Image URL"
              type="url"
              value={values.featured_image_url || ''}
              onChange={(e) => handleFieldChange('featured_image_url', e.target.value)}
              error={getFieldError('featured_image_url')}
              isValid={isFieldValid('featured_image_url')}
              placeholder="https://example.com/image.jpg"
              description="URL of the main image for this trip"
            />
          </div>
        </FormSection>

        {/* Form Actions */}
        <div className="flex justify-between items-center pt-6 border-t border-gray-200">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            {isValid ? (
              <>
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                Form is valid
              </>
            ) : (
              <>
                <AlertTriangle className="h-4 w-4 text-amber-600" />
                Please fix validation errors
              </>
            )}
          </div>

          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              disabled={isLoading || !isValid}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <LoadingSpinner size="small" className="mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {mode === 'create' ? 'Create Trip' : 'Update Trip'}
                </>
              )}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}

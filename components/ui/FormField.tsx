'use client';

import React from 'react';
import { AlertCircle, CheckCircle2 } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface FormFieldProps {
  label: string;
  name: string;
  required?: boolean;
  error?: string;
  isValid?: boolean;
  showValidation?: boolean;
  children: React.ReactNode;
  description?: string;
  className?: string;
  labelClassName?: string;
  errorClassName?: string;
}

export function FormField({
  label,
  name,
  required = false,
  error,
  isValid,
  showValidation = true,
  children,
  description,
  className,
  labelClassName,
  errorClassName
}: FormFieldProps) {
  const hasError = !!error;
  const showSuccess = showValidation && isValid && !hasError;
  const showError = showValidation && hasError;

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label with required indicator */}
      <label 
        htmlFor={name}
        className={cn(
          'block text-sm font-medium text-gray-700',
          labelClassName
        )}
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>

      {/* Description */}
      {description && (
        <p className="text-sm text-gray-500">
          {description}
        </p>
      )}

      {/* Input wrapper with validation styling */}
      <div className="relative">
        {React.cloneElement(children as React.ReactElement, {
          id: name,
          name: name,
          className: cn(
            (children as React.ReactElement).props.className,
            // Base styling
            'block w-full rounded-md border-gray-300 shadow-sm focus:ring-2 focus:ring-offset-0',
            // Error styling
            showError && 'border-red-300 focus:border-red-500 focus:ring-red-500',
            // Success styling
            showSuccess && 'border-green-300 focus:border-green-500 focus:ring-green-500',
            // Default focus styling when no validation
            !showValidation && 'focus:border-blue-500 focus:ring-blue-500'
          ),
          'aria-invalid': showError,
          'aria-describedby': showError ? `${name}-error` : undefined
        })}

        {/* Validation icons */}
        {showValidation && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            {showError && (
              <AlertCircle className="h-5 w-5 text-red-500" aria-hidden="true" />
            )}
            {showSuccess && (
              <CheckCircle2 className="h-5 w-5 text-green-500" aria-hidden="true" />
            )}
          </div>
        )}
      </div>

      {/* Error message */}
      {showError && (
        <p 
          id={`${name}-error`}
          className={cn(
            'text-sm text-red-600 flex items-center gap-1',
            errorClassName
          )}
          role="alert"
        >
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          {error}
        </p>
      )}
    </div>
  );
}

// Specialized input components with built-in FormField wrapper
export interface FormInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'name'> {
  name: string;
  label: string;
  required?: boolean;
  error?: string;
  isValid?: boolean;
  showValidation?: boolean;
  description?: string;
  fieldClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
}

export function FormInput({
  name,
  label,
  required,
  error,
  isValid,
  showValidation,
  description,
  fieldClassName,
  labelClassName,
  errorClassName,
  className,
  ...inputProps
}: FormInputProps) {
  return (
    <FormField
      name={name}
      label={label}
      required={required}
      error={error}
      isValid={isValid}
      showValidation={showValidation}
      description={description}
      className={fieldClassName}
      labelClassName={labelClassName}
      errorClassName={errorClassName}
    >
      <input
        type="text"
        className={className}
        {...inputProps}
      />
    </FormField>
  );
}

export interface FormTextareaProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'name'> {
  name: string;
  label: string;
  required?: boolean;
  error?: string;
  isValid?: boolean;
  showValidation?: boolean;
  description?: string;
  fieldClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
}

export function FormTextarea({
  name,
  label,
  required,
  error,
  isValid,
  showValidation,
  description,
  fieldClassName,
  labelClassName,
  errorClassName,
  className,
  ...textareaProps
}: FormTextareaProps) {
  return (
    <FormField
      name={name}
      label={label}
      required={required}
      error={error}
      isValid={isValid}
      showValidation={showValidation}
      description={description}
      className={fieldClassName}
      labelClassName={labelClassName}
      errorClassName={errorClassName}
    >
      <textarea
        className={className}
        {...textareaProps}
      />
    </FormField>
  );
}

export interface FormSelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'name'> {
  name: string;
  label: string;
  required?: boolean;
  error?: string;
  isValid?: boolean;
  showValidation?: boolean;
  description?: string;
  fieldClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  placeholder?: string;
}

export function FormSelect({
  name,
  label,
  required,
  error,
  isValid,
  showValidation,
  description,
  fieldClassName,
  labelClassName,
  errorClassName,
  className,
  options,
  placeholder,
  ...selectProps
}: FormSelectProps) {
  return (
    <FormField
      name={name}
      label={label}
      required={required}
      error={error}
      isValid={isValid}
      showValidation={showValidation}
      description={description}
      className={fieldClassName}
      labelClassName={labelClassName}
      errorClassName={errorClassName}
    >
      <select
        className={className}
        {...selectProps}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option 
            key={option.value} 
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </select>
    </FormField>
  );
}

// Form section component for grouping related fields
export interface FormSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export function FormSection({
  title,
  description,
  children,
  className,
  collapsible = false,
  defaultExpanded = true
}: FormSectionProps) {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);

  return (
    <div className={cn('space-y-6', className)}>
      <div className="border-b border-gray-200 pb-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">{title}</h3>
            {description && (
              <p className="mt-1 text-sm text-gray-500">{description}</p>
            )}
          </div>
          {collapsible && (
            <button
              type="button"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </button>
          )}
        </div>
      </div>
      
      {(!collapsible || isExpanded) && (
        <div className="space-y-6">
          {children}
        </div>
      )}
    </div>
  );
}

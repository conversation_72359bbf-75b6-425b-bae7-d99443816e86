'use client';

import { useState, useCallback, useEffect } from 'react';
import { z } from 'zod';

export interface ValidationError {
  field: string;
  message: string;
}

export interface FormValidationOptions<T> {
  schema: z.ZodSchema<T>;
  onValidationChange?: (isValid: boolean, errors: ValidationError[]) => void;
  validateOnChange?: boolean;
  preserveProgress?: boolean;
  storageKey?: string;
}

export interface FormValidationResult<T> {
  values: Partial<T>;
  errors: Record<string, string>;
  isValid: boolean;
  isFieldValid: (field: keyof T) => boolean;
  getFieldError: (field: keyof T) => string | undefined;
  validateField: (field: keyof T, value: any) => string | undefined;
  validateForm: () => boolean;
  updateField: (field: keyof T, value: any) => void;
  updateFields: (fields: Partial<T>) => void;
  setValues: (values: Partial<T>) => void;
  clearErrors: () => void;
  clearField: (field: keyof T) => void;
  reset: (newValues?: Partial<T>) => void;
  hasUnsavedChanges: boolean;
  markAsSaved: () => void;
}

export function useFormValidation<T extends Record<string, any>>(
  initialValues: Partial<T>,
  options: FormValidationOptions<T>
): FormValidationResult<T> {
  const {
    schema,
    onValidationChange,
    validateOnChange = true,
    preserveProgress = true,
    storageKey
  } = options;

  // Load saved progress if available
  const loadSavedProgress = useCallback((): Partial<T> => {
    if (!preserveProgress || !storageKey || typeof window === 'undefined') {
      return initialValues;
    }

    try {
      const saved = localStorage.getItem(`form_progress_${storageKey}`);
      if (saved) {
        const parsedSaved = JSON.parse(saved);
        return { ...initialValues, ...parsedSaved };
      }
    } catch (error) {
      console.warn('Failed to load saved form progress:', error);
    }

    return initialValues;
  }, [initialValues, preserveProgress, storageKey]);

  const [values, setValuesState] = useState<Partial<T>>(loadSavedProgress);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValid, setIsValid] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Save progress to localStorage
  const saveProgress = useCallback((newValues: Partial<T>) => {
    if (!preserveProgress || !storageKey || typeof window === 'undefined') {
      return;
    }

    try {
      localStorage.setItem(`form_progress_${storageKey}`, JSON.stringify(newValues));
    } catch (error) {
      console.warn('Failed to save form progress:', error);
    }
  }, [preserveProgress, storageKey]);

  // Clear saved progress
  const clearProgress = useCallback(() => {
    if (!preserveProgress || !storageKey || typeof window === 'undefined') {
      return;
    }

    try {
      localStorage.removeItem(`form_progress_${storageKey}`);
    } catch (error) {
      console.warn('Failed to clear form progress:', error);
    }
  }, [preserveProgress, storageKey]);

  // Validate a single field
  const validateField = useCallback((field: keyof T, value: any): string | undefined => {
    try {
      // Create a partial schema for the specific field
      const fieldSchema = schema.pick({ [field]: true } as any);
      fieldSchema.parse({ [field]: value });
      return undefined;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldError = error.errors.find(err => err.path.includes(field as string));
        return fieldError?.message;
      }
      return 'Invalid value';
    }
  }, [schema]);

  // Validate entire form
  const validateForm = useCallback((): boolean => {
    try {
      schema.parse(values);
      setErrors({});
      setIsValid(true);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach(err => {
          const field = err.path.join('.');
          newErrors[field] = err.message;
        });
        setErrors(newErrors);
        setIsValid(false);
        return false;
      }
      setIsValid(false);
      return false;
    }
  }, [values, schema]);

  // Update a single field
  const updateField = useCallback((field: keyof T, value: any) => {
    const newValues = { ...values, [field]: value };
    setValuesState(newValues);
    setHasUnsavedChanges(true);
    saveProgress(newValues);

    if (validateOnChange) {
      const fieldError = validateField(field, value);
      setErrors(prev => ({
        ...prev,
        [field]: fieldError || ''
      }));

      // Remove empty error strings
      setErrors(prev => {
        const cleaned = { ...prev };
        if (!cleaned[field as string]) {
          delete cleaned[field as string];
        }
        return cleaned;
      });
    }
  }, [values, validateField, validateOnChange, saveProgress]);

  // Update multiple fields
  const updateFields = useCallback((fields: Partial<T>) => {
    const newValues = { ...values, ...fields };
    setValuesState(newValues);
    setHasUnsavedChanges(true);
    saveProgress(newValues);

    if (validateOnChange) {
      const newErrors = { ...errors };
      Object.entries(fields).forEach(([field, value]) => {
        const fieldError = validateField(field as keyof T, value);
        if (fieldError) {
          newErrors[field] = fieldError;
        } else {
          delete newErrors[field];
        }
      });
      setErrors(newErrors);
    }
  }, [values, errors, validateField, validateOnChange, saveProgress]);

  // Set all values (useful for loading initial data)
  const setValues = useCallback((newValues: Partial<T>) => {
    setValuesState(newValues);
    setHasUnsavedChanges(false);
    saveProgress(newValues);
  }, [saveProgress]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Clear specific field error
  const clearField = useCallback((field: keyof T) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field as string];
      return newErrors;
    });
  }, []);

  // Reset form
  const reset = useCallback((newValues?: Partial<T>) => {
    const resetValues = newValues || initialValues;
    setValuesState(resetValues);
    setErrors({});
    setIsValid(false);
    setHasUnsavedChanges(false);
    clearProgress();
  }, [initialValues, clearProgress]);

  // Mark as saved (clear unsaved changes flag)
  const markAsSaved = useCallback(() => {
    setHasUnsavedChanges(false);
    clearProgress();
  }, [clearProgress]);

  // Helper functions
  const isFieldValid = useCallback((field: keyof T): boolean => {
    return !errors[field as string];
  }, [errors]);

  const getFieldError = useCallback((field: keyof T): string | undefined => {
    return errors[field as string];
  }, [errors]);

  // Effect to notify validation changes
  useEffect(() => {
    if (onValidationChange) {
      const validationErrors: ValidationError[] = Object.entries(errors).map(([field, message]) => ({
        field,
        message
      }));
      onValidationChange(isValid, validationErrors);
    }
  }, [isValid, errors, onValidationChange]);

  return {
    values,
    errors,
    isValid,
    isFieldValid,
    getFieldError,
    validateField,
    validateForm,
    updateField,
    updateFields,
    setValues,
    clearErrors,
    clearField,
    reset,
    hasUnsavedChanges,
    markAsSaved
  };
}
